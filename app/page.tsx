"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, MapPin, Users, Calendar } from "lucide-react"
import { HeroCarousel } from "@/components/hero-carousel"
import { useAuthRedirect } from "@/lib/domains/auth/auth.hooks"
import { PageLoading } from "@/components/page-loading"

function HomePageContent() {
  const { loading, isRedirecting } = useAuthRedirect()

  // Show loading while checking auth state or redirecting
  if (loading || isRedirecting) {
    return <PageLoading message="Loading..." />
  }

  return (
    <>
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 fixed top-0 left-0 right-0 z-50">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <MapPin className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold">Togeda.ai</h1>
          </div>
          <div className="flex gap-4 items-center">
            <Link href="/login">
              <Button variant="ghost">Login</Button>
            </Link>
            <Link href="/signup">
              <Button>Sign Up</Button>
            </Link>
            {/* <Link
              href="/admin/login"
              className="text-xs text-muted-foreground hover:text-foreground ml-2"
            >
              Admin
            </Link> */}
          </div>
        </div>
      </header>

      <div className="flex flex-col min-h-screen pt-[73px]">
        {/* Hero Section */}
        <section className="py-16 md:py-24 bg-primary text-primary-foreground dark:bg-background dark:text-foreground">
          <div className="container mx-auto px-4 flex flex-col md:flex-row items-center gap-12">
            <div className="w-full md:flex-1 space-y-6">
              <h1 className="text-4xl md:text-6xl font-bold tracking-tight text-primary-foreground dark:text-foreground">
                Plan epic trips with your squad
              </h1>
              <p className="text-xl text-primary-foreground/80 dark:text-muted-foreground">
                Togeda.ai helps you and your friends plan the perfect getaway with AI-powered
                recommendations tailored to your group's preferences.
              </p>
              <div className="flex gap-4">
                <Link href="/signup">
                  <Button
                    size="lg"
                    className="gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-medium transition-all duration-300 ease-in-out hover:scale-105 active:scale-95"
                  >
                    Get Started <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
                <Link href="#how-it-works">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10 hover:text-primary-foreground dark:border-border dark:text-foreground dark:hover:bg-accent dark:hover:text-accent-foreground"
                  >
                    How It Works
                  </Button>
                </Link>
              </div>
            </div>
            <div className="w-full md:flex-1 mt-8 md:mt-0 relative">
              <div className="relative z-10">
                <HeroCarousel
                  images={[
                    {
                      src: "/homepage-image01.jpg",
                      alt: "Friends enjoying a trip together in the mountains",
                      caption: "Create unforgettable memories with your squad",
                      attribution: "Pexels",
                      attributionUrl:
                        "https://www.pexels.com/photo/four-person-standing-at-top-of-grassy-mountain-697244/",
                    },
                    {
                      src: "/homepage-image02.jpg",
                      alt: "Group planning a trip together",
                      caption: "Plan your perfect getaway with AI assistance",
                      attribution: "Pexels",
                      attributionUrl: "https://www.pexels.com/photo/aerial-seashore-883758/",
                    },
                    {
                      src: "/homepage-image03.jpg",
                      alt: "Friends on a road trip adventure",
                      caption: "Organize every detail of your trip in one place",
                      attribution: "Pexels",
                      attributionUrl:
                        "https://www.pexels.com/photo/close-up-of-a-map-and-multicoloured-pins-8828425/",
                    },
                  ]}
                />
              </div>
              <div className="absolute -bottom-6 -right-6 w-3/4 h-3/4 bg-primary-foreground/20 dark:bg-muted/20 rounded-lg -z-10"></div>
            </div>
          </div>
        </section>

        {/* Features */}
        <section id="how-it-works" className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">How Togeda.ai Works</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center text-center p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Create Your Squad</h3>
                <p className="text-muted-foreground">
                  Add friends to your squad and get ready to plan your next adventure together.
                </p>
              </div>
              <div className="flex flex-col items-center text-center p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <MapPin className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Get AI Recommendations</h3>
                <p className="text-muted-foreground">
                  Our AI suggests perfect trips based on everyone's preferences, budget, and
                  schedule.
                </p>
              </div>
              <div className="flex flex-col items-center text-center p-6 rounded-lg border bg-card">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <Calendar className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Plan & Execute</h3>
                <p className="text-muted-foreground">
                  Assign tasks, track progress, and make your trip happen with minimal hassle.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA */}
        <section className="py-16 bg-primary/10">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to plan your next adventure?</h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join Togeda.ai today and start planning unforgettable trips with your friends.
            </p>
            <Link href="/signup">
              <Button size="lg">Get Started</Button>
            </Link>
          </div>
        </section>

        {/* Feedback Section */}
        <section className="py-12 bg-background">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-4">We'd Like to Have Your Feedback</h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Help us improve Togeda.ai by sharing your thoughts and suggestions.
            </p>
            <Link href="https://vdc.formaloo.me/brotrips-ai-feedback">
              <Button size="lg" variant="outline">
                Share Your Feedback
              </Button>
            </Link>
          </div>
        </section>

        {/* Footer */}
        <footer className="mt-auto border-t py-8">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center gap-2 mb-4 md:mb-0">
                <MapPin className="h-5 w-5 text-primary" />
                <span className="font-semibold">Togeda.ai</span>
              </div>
              <div className="flex gap-6">
                <Link href="#" className="text-muted-foreground hover:text-foreground">
                  About
                </Link>
                <Link href="/privacy" className="text-muted-foreground hover:text-foreground">
                  Privacy
                </Link>
                <Link href="/terms" className="text-muted-foreground hover:text-foreground">
                  Terms
                </Link>
                <Link
                  href="https://vdc.formaloo.me/brotrips-ai-contact-us"
                  className="text-muted-foreground hover:text-foreground"
                >
                  Contact
                </Link>
                {/* <Link href="/admin/login" className="text-muted-foreground hover:text-foreground">
                Admin
              </Link> */}
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  )
}

export default function HomePage() {
  return <HomePageContent />
}
