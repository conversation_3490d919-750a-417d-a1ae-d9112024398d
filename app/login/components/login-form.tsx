"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { signInWithEmailAndPassword } from "firebase/auth"
import { auth } from "@/lib/firebase"
import { toast } from "@/components/ui/use-toast"
import { UserService } from "@/lib/domains/user/user.service"
import { Logo } from "@/components/ui/logo"

export function LoginForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [callbackUrl, setCallbackUrl] = useState<string | null>(null)
  const [loginMessage, setLoginMessage] = useState<string | null>(null)

  // Message codes and their corresponding messages
  const messageMap: Record<string, string> = {
    needs_login: "Please log in to continue.",
    session_expired: "Your session has expired. Please log in again.",
    account_created: "Account created successfully! Please log in with your credentials.",
    invitation_access: "Please log in to view and respond to the invitation.",
    unauthorized: "You need to be logged in to access this page.",
  }

  // Extract callback URL and message code from search params
  useEffect(() => {
    // Handle callback URL
    const callback = searchParams.get("callback")
    if (callback) {
      console.log(`Login page received callback: ${callback}`)
      setCallbackUrl(callback)
    }

    // Handle message code
    const messageCode = searchParams.get("message")
    if (messageCode && messageMap[messageCode]) {
      console.log(`Login page received message code: ${messageCode}`)
      setLoginMessage(messageMap[messageCode])
    }
  }, [searchParams])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password) {
      toast({
        title: "Missing information",
        description: "Please enter your email and password",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      const user = userCredential.user

      toast({
        title: "Login successful",
        description: "Welcome back to Togeda.ai!",
      })

      // Check if user is new and should see welcome experience
      const isNewUser = await UserService.isNewUser(user.uid)

      // Update first login if needed
      await UserService.updateFirstLogin(user.uid)

      // Redirect logic
      if (callbackUrl) {
        console.log(`Login successful, redirecting to callback: ${callbackUrl}`)
        router.push(callbackUrl)
      } else if (isNewUser) {
        console.log(`New user detected, redirecting to welcome`)
        router.push("/welcome")
      } else {
        console.log(`Login successful, redirecting to dashboard`)
        router.push("/dashboard")
      }
    } catch (error: any) {
      console.error("Login error:", error)
      toast({
        title: "Login unsuccessful",
        description: "Invalid email or password",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/">
            <Logo width={24} height={24} />
          </Link>
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Login to Togeda.ai</CardTitle>
            <CardDescription>
              {loginMessage || "Enter your credentials to access your account"}
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleLogin}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <Link href="/forgot-password" className="text-xs text-primary hover:underline">
                    Forgot password?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? "Logging in..." : "Login"}
              </Button>
              <div className="text-center text-sm">
                Don't have an account?{" "}
                <Link href="/signup" className="text-primary hover:underline">
                  Sign up
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </main>
    </div>
  )
}
