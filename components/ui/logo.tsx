"use client"

import Image from "next/image"
import { cn } from "@/lib/utils"

interface LogoProps {
  className?: string
  width?: number
  height?: number
  priority?: boolean
  showText?: boolean
  textClassName?: string
}

export function Logo({ 
  className, 
  width = 32, 
  height = 32, 
  priority = false,
  showText = false,
  textClassName 
}: LogoProps) {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Image
        src="/togeda_logo.png"
        alt="Togeda.ai"
        width={width}
        height={height}
        priority={priority}
        className="object-contain"
      />
      {showText && (
        <span className={cn("font-semibold", textClassName)}>
          Togeda.ai
        </span>
      )}
    </div>
  )
}
